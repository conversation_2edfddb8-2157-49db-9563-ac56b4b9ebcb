/*
 * WiFi AP Mode Configuration for ESP32 File Server with OTA
 */

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_mac.h"

#include "lwip/err.h"
#include "lwip/sys.h"
#include "dev_id.h"

/*
 * WiFi 2.4GHz信道与BLE干扰分析：
 * BLE使用2402-2480MHz，分为40个信道，每个信道间隔2MHz
 * WiFi 2.4GHz信道：
 * - 信道1: 2412MHz (与BLE信道5重叠，干扰中等)
 * - 信道6: 2437MHz (与BLE信道18重叠，干扰较大)
 * - 信道11: 2462MHz (与BLE信道35重叠，干扰较大)
 * - 信道13: 2472MHz (与BLE信道37重叠，但BLE信道37是广播信道，可优化)
 *
 * 选择信道13的原因：
 * 1. 远离WiFi常用信道1,6,11，减少WiFi设备间干扰
 * 2. 虽然与BLE广播信道37重叠，但可以通过BLE参数优化
 * 3. 在中国地区合法使用
 */


/* WiFi AP configuration */
#define WIFI_AP_SSID "ESP32-C3-OTA"
#define WIFI_AP_PASS "12345678"      // 无密码 - 开放网络
#define WIFI_AP_CHANNEL 13           // 使用信道13，与BLE频段干扰最小
#define WIFI_AP_MAX_CONN 2           // 标准最大连接数
#define WIFI_AP_BEACON_INTERVAL 1000 // 标准beacon间隔以确保可发现性 (默认100ms)
#define WIFI_AP_DTIM_PERIOD 3        // 设置DTIM周期以优化功耗

static const char *TAG = "wifi_ap";

/* Global variables for WiFi management */
static esp_netif_t *wifi_netif = NULL;
static esp_event_handler_instance_t instance_any_id = NULL;
static bool wifi_initialized = false;

static void wifi_event_handler(void* arg, esp_event_base_t event_base,int32_t event_id, void* event_data)
{
    if (event_id == WIFI_EVENT_AP_STACONNECTED) {
        wifi_event_ap_staconnected_t* event = (wifi_event_ap_staconnected_t*) event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x joined, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    } 
    else if (event_id == WIFI_EVENT_AP_STADISCONNECTED) {
        wifi_event_ap_stadisconnected_t* event = (wifi_event_ap_stadisconnected_t*) event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x left, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    }
}

esp_err_t wifi_init_ap(void)
{
    esp_err_t ret;

    // 检查是否已经初始化
    if (wifi_initialized) {
        ESP_LOGW(TAG, "WiFi AP already initialized");
        return ESP_OK;
    }

    // 初始化网络接口和事件循环（ESP-IDF会处理重复调用）
    ret = esp_netif_init();
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "Failed to initialize netif: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_event_loop_create_default();
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "Failed to create event loop: %s", esp_err_to_name(ret));
        return ret;
    }

    // 创建WiFi AP网络接口（只创建一次）
    if (wifi_netif == NULL) {
        wifi_netif = esp_netif_create_default_wifi_ap();
        if (wifi_netif == NULL) {
            ESP_LOGE(TAG, "Failed to create WiFi AP netif");
            return ESP_FAIL;
        }
    }

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ret = esp_wifi_init(&cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册事件处理器（只注册一次）
    if (instance_any_id == NULL) {
        ret = esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL, &instance_any_id);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to register WiFi event handler: %s", esp_err_to_name(ret));
            esp_wifi_deinit();
            return ret;
        }
    }

   wifi_config_t wifi_config = {
        .ap = {
            .ssid = WIFI_AP_SSID,
            .ssid_len = strlen(WIFI_AP_SSID),
            .channel = WIFI_AP_CHANNEL, // 固定信道13，减少与BLE干扰
            .password = WIFI_AP_PASS,
            .max_connection = WIFI_AP_MAX_CONN,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .ssid_hidden = 0,                           // 确保SSID可见
            .beacon_interval = WIFI_AP_BEACON_INTERVAL, // 标准beacon间隔
            .pairwise_cipher = WIFI_CIPHER_TYPE_NONE,   // 开放网络无加密
            .ftm_responder = false,                     // 禁用FTM
            .pmf_cfg = {
                .capable = false,
                .required = false,
            },
        },
    };

    // 修改 .ssid 为 TPM_Jar{device_id}
    uint16_t device_id;
    ret = get_device_id(&device_id);
    if (ret == ESP_OK) {
        char dynamic_ssid[32];
        int len = snprintf(dynamic_ssid, sizeof(dynamic_ssid), "TPM_Jar%d", device_id);
        
        // 检查SSID长度是否超限
        if (len >= sizeof(dynamic_ssid)) {
            ESP_LOGW(TAG, "SSID truncated, device_id too large: %d", device_id);
        }
        
        // 检查WiFi SSID长度限制 (最大32字节)
        if (len > 32) {
            ESP_LOGE(TAG, "Generated SSID too long (%d bytes), using default", len);
        } else {
            // 更新WiFi配置中的SSID
            strncpy((char*)wifi_config.ap.ssid, dynamic_ssid, sizeof(wifi_config.ap.ssid) - 1);
            wifi_config.ap.ssid[sizeof(wifi_config.ap.ssid) - 1] = '\0'; // 确保字符串结束
            wifi_config.ap.ssid_len = strlen(dynamic_ssid);
            ESP_LOGI(TAG, "Using dynamic SSID: %s", dynamic_ssid);
        }
    } else {
        ESP_LOGW(TAG, "Failed to get device_id (%s), using default SSID: %s", 
                 esp_err_to_name(ret), WIFI_AP_SSID);
    }

    ESP_LOGI(TAG, "Setting WiFi mode to AP...");
    ret = esp_wifi_set_mode(WIFI_MODE_AP);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi mode: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "Setting WiFi configuration...");
    ret = esp_wifi_set_config(WIFI_IF_AP, &wifi_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi config: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "Starting WiFi...");
    ret = esp_wifi_start();
    if (ret != ESP_OK){
        ESP_LOGE(TAG, "Failed to start WiFi: %s", esp_err_to_name(ret));
        return ret;
    }

    // 等待WiFi启动
    vTaskDelay(pdMS_TO_TICKS(1000));

    // 设置国家代码确保信道13可用
    wifi_country_t country = {
        .cc = "CN",                           // 中国
        .schan = 1,                           // 起始信道
        .nchan = WIFI_AP_CHANNEL,             // 信道数量
        .max_tx_power = 84,                   // 最大发射功率 (21dBm)
        .policy = WIFI_COUNTRY_POLICY_MANUAL, // 手动策略，强制使用指定信道
    };
    ret = esp_wifi_set_country(&country);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi country: %s", esp_err_to_name(ret));
        // 国家代码设置失败可能影响信道13的使用，但继续执行
    } else {
        ESP_LOGI(TAG, "WiFi country set to CN, channel 13 enabled");
    }

    // 强制设置带宽为20MHz以减少频谱占用
    ret = esp_wifi_set_bandwidth(WIFI_IF_AP, WIFI_BW_HT20);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi bandwidth: %s", esp_err_to_name(ret));
        // 带宽设置失败可能影响BLE干扰，但继续执行
    } else {
        ESP_LOGI(TAG, "WiFi bandwidth fixed to 20MHz");
    }

    // 设置WiFi协议，禁用高速模式以减少干扰
    ret = esp_wifi_set_protocol(WIFI_IF_AP, WIFI_PROTOCOL_11B | WIFI_PROTOCOL_11G);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi protocol: %s", esp_err_to_name(ret));
        // 协议设置失败可能影响稳定性，但继续执行
    } else {
        ESP_LOGI(TAG, "WiFi protocol set to 802.11b/g (no 11n for stability)");
    }

    // 检查WiFi状态
    wifi_mode_t mode;
    ret = esp_wifi_get_mode(&mode);
    if (ret == ESP_OK){
        ESP_LOGI(TAG, "WiFi mode: %d", mode);
    }

    // 获取AP信息
    wifi_config_t get_conf;
    ret = esp_wifi_get_config(WIFI_IF_AP, &get_conf);
    if (ret == ESP_OK){
        ESP_LOGI(TAG, "AP Config - SSID: %s, Channel: %d, Auth: %d",(char *)get_conf.ap.ssid, get_conf.ap.channel, get_conf.ap.authmode);
    }

    //
    wifi_initialized = true;
    return ESP_OK;
}

esp_err_t wifi_deinit_ap(void)
{
    esp_err_t ret;

    if (!wifi_initialized) {
        ESP_LOGW(TAG, "WiFi AP not initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping WiFi AP...");

    // Stop WiFi
    ret = esp_wifi_stop();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop WiFi: %s", esp_err_to_name(ret));
        // Continue with cleanup even if stop fails
    }

    // Unregister event handler
    if (instance_any_id != NULL) {
        ret = esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, instance_any_id);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to unregister WiFi event handler: %s", esp_err_to_name(ret));
        }
        instance_any_id = NULL;
    }

    // Deinitialize WiFi
    ret = esp_wifi_deinit();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to deinitialize WiFi: %s", esp_err_to_name(ret));
        // Continue with cleanup even if deinit fails
    }

    // Destroy WiFi netif
    if (wifi_netif != NULL) {
        esp_netif_destroy_default_wifi(wifi_netif);
        wifi_netif = NULL;
    }

    wifi_initialized = false;
    ESP_LOGI(TAG, "WiFi AP stopped successfully");
    return ESP_OK;
}
