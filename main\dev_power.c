#include "dev_power.h"
#include "esp_log.h"
#include "freertos/queue.h"

/* LED GPIO定义 */
#define DEV_POWER_BUTTON GPIO_NUM_9
#define DEV_POWER_ENABLE GPIO_NUM_16

/* 按键防抖时间 (毫秒) */
#define BUTTON_DEBOUNCE_TIME_MS 50

static const char *TAG = "DEV_POWER";
static bool led_initialized = false;
static uint32_t last_button_press_time = 0; // 上次按键时间

/**
 * @brief 按键中断处理函数
 */
static void IRAM_ATTR power_button_isr_handler(void *arg)
{
    uint32_t current_time = xTaskGetTickCountFromISR() * portTICK_PERIOD_MS;

    // 防抖处理：检查距离上次按键是否超过防抖时间
    if (current_time - last_button_press_time > BUTTON_DEBOUNCE_TIME_MS)
    {
        last_button_press_time = current_time;

        // 这里是不是可以保存

        // 设置电源使能引脚
        gpio_set_level(DEV_POWER_ENABLE, POWER_OFF);

        ESP_EARLY_LOGW(TAG, "Power button pressed, shutting down");
    }
}

//
esp_err_t dev_power_init(void)
{
    if (led_initialized)
    {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    //
    gpio_config_t power_button_io_conf = {
        .intr_type = GPIO_INTR_NEGEDGE, // 下降沿触发
        .mode = GPIO_MODE_INPUT,        // 输入模式
        .pin_bit_mask = (1ULL << DEV_POWER_BUTTON),
        .pull_down_en = 0,
        .pull_up_en = GPIO_PULLUP_ENABLE,
    };

    //
    gpio_config_t power_enable_io_conf = {
        .intr_type = GPIO_INTR_DISABLE, // 禁用中断
        .mode = GPIO_MODE_OUTPUT,       // 输出模式
        .pin_bit_mask = (1ULL << DEV_POWER_ENABLE),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };

    esp_err_t ret = gpio_config(&power_button_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = gpio_config(&power_enable_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 安装GPIO中断服务
    ret = gpio_install_isr_service(0); // 0 = 不需要特别设置优先级，不需要放 IRAM，不需要共享，默认中断服务
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE)
    {
        ESP_LOGE(TAG, "Failed to install ISR service: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册按键中断处理函数
    ret = gpio_isr_handler_add(DEV_POWER_BUTTON, power_button_isr_handler, NULL);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to add ISR handler: %s", esp_err_to_name(ret));
        return ret;
    }

    // 设置初始电源状态
    ret = gpio_set_level(DEV_POWER_ENABLE, POWER_ON);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set initial power state: %s", esp_err_to_name(ret));
        return ret;
    }

    led_initialized = true;
    ESP_LOGI(TAG, "GPIO%d initialized as power output", DEV_POWER_ENABLE);

    return ESP_OK;
}
