# ninja log v6
27	279	7781627709926311	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b8ba88626490e6d2
33	316	7781627709986316	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	25e11b047edc83c0
48	345	7781627710133399	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	937bd73ff42bf221
56	395	7781627710213365	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	73d2ea14d681591b
64	421	7781627710293370	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	649cdad656f8b4f8
72	454	7781627710363386	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	58f01bb682f2e3d7
82	487	7781627710473370	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e1b94d12daf9ffd8
91	507	7781627710563381	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cdfb41a536fb8bbd
104	556	7781627710693409	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	5584ae294dc8bc2c
117	607	7781627710813382	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	210943843935fd9b
131	641	7781627710963395	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	673ea9e469617ab
164	672	7781627711293397	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	483192b6cbcd8ea8
200	761	7781627711653378	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cfdd251618f6a714
220	795	7781627711853401	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	497155e73f89de4d
280	831	7781627712453382	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	14c31d09cb487174
21	847	7781627709866326	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	30f51d844e329496
316	878	7781627712813389	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	6aa3570ab1cd2ae2
346	919	7781627713113381	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ca32f6349de14497
40	947	7781627710056327	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0886aec47ac34ed
8	970	7781627709736333	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	cb78176f3653dddc
396	985	7781627713603426	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	c0df74edbaa8d4e2
16	1009	7781627709806310	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	31643bd8ceda0e35
421	1052	7781627713863390	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	4bd0cfcf61b4f9eb
454	1071	7781627714193577	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8a2798b39b38967
487	1092	7781627714523600	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	83e187d5649e4953
507	1126	7781627714723604	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	63f8478ca2cd3234
556	1149	7781627715213608	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	fda948f7d94318fe
607	1190	7781627715723597	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4824227fe51f023e
641	1232	7781627716063566	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	142b08fee5111a88
762	1266	7781627717263559	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4b64013a1931ce9e
795	1311	7781627717603604	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	bf5475bb65ab941c
831	1337	7781627717963580	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	647702da2488015a
847	1373	7781627718128554	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	83781b0edbf4dd06
879	1409	7781627718438561	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	dcae97481a03c066
920	1433	7781627718848589	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	fc218c3d43777ba7
948	1484	7781627719123847	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	70b39228ff3c3953
970	1517	7781627719353842	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6790c1df7306deb2
985	1549	7781627719503850	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	b967448cdfaa7383
1009	1583	7781627719743848	esp-idf/log/liblog.a	c91a251e1ec6f52c
1052	1715	7781627720179126	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	606a76877fef8d81
1071	1743	7781627720359116	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a0cddf8baf8a7725
1092	1771	7781627720569140	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	ffdd27642721c0bf
1126	1805	7781627720909120	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b77bc6b182498ad1
1149	1872	7781627721139130	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4ebae100feb37f9a
1190	1906	7781627721559116	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	7a3f3769d1ee15cd
1232	1934	7781627721969107	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	233500e586f9ee34
1266	1951	7781627722309131	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	abca8987946c4
673	1964	7781627716373566	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	858d4baa72e0a17b
1311	1993	7781627722759140	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	889baa59c793157
1337	2017	7781627723019097	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	1b857d8603efa092
1373	2030	7781627723389117	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	b80ca273a9dbb954
1409	2044	7781627723749126	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	6e27940ab0c3e7c
1433	2059	7781627723979142	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	749b6846e469439c
1484	2086	7781627724489131	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	7337709c27fee1ca
1517	2134	7781627724829124	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	25b380721e1b29b6
1549	2157	7781627725149121	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	35c22edb96f4c16a
1583	2202	7781627725475854	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
1715	2320	7781627726805804	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	be38ffb92a960c1f
1743	2349	7781627727075841	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	28f496dae6971f53
1771	2380	7781627727361201	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	8f538409bc2cd6cc
1805	2406	7781627727701166	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61d58f48e37c570c
1872	2432	7781627728376478	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	a1902cbe249b4406
1906	2452	7781627728706487	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f7cd516dfa5aa97
1951	2475	7781627729156458	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	24015bfc507f4dc0
1964	2496	7781627729296474	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	885af7f345ad7210
1994	2513	7781627729586469	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	445508437b2f4d37
2018	2554	7781627729826467	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	6d24e56d1ded1fb9
2030	2634	7781627729956507	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	97f79caf25082d69
2044	2657	7781627730096497	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	1abbd206e65300d7
2059	2657	7781627730239283	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	29b8fde4d883d840
2086	2658	7781627730519282	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	754ba61bf0dd9e65
2134	2658	7781627730989262	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	d3b413fd7717610e
2157	2658	7781627731229260	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	2fbd10801eda5f93
2202	2659	7781627731669273	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
2320	2707	7781627732849357	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	9da01f23b4cebb82
2349	2707	7781627733149275	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	36a0b2d6be3ef1b
2380	2708	7781627733449280	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	58accd046d586db1
2407	2708	7781627733719273	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	e9fa507b2c68099a
2432	2708	7781627733969251	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	d880b4553289daf1
2453	2709	7781627734179268	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	1af531e95af75350
2475	2709	7781627734399269	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	88ce9b6940f96e5c
2496	2710	7781627734609270	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	7ab51ff84ae785a9
2554	2710	7781627736472477	project_elf_src_esp32c3.c	6c7d774e998bc721
2554	2710	7781627736472477	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	6c7d774e998bc721
2513	2736	7781627734779270	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b9f0ed134b19d3fd
1934	2778	7781627728996466	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	2846994fd4688db
2634	2789	7781627735992505	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	74d83039a25dbef3
2710	2813	7781627736752469	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	fadd1928e73520b8
2659	2865	7781627736242472	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
2865	2953	7781627738302731	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
2953	3046	7781627739178191	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
3046	3174	7781627740109409	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
3174	3262	7781627741386041	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
3262	3342	7781627742266067	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
3342	3437	7781627743076099	esp-idf/hal/libhal.a	38abfeb38a2a248d
3437	3530	7781627744026071	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
3530	3658	7781627744946066	esp-idf/soc/libsoc.a	2ed56a05eac453da
3658	3738	7781627746228354	esp-idf/main/libmain.a	5bc613d7954c6145
3738	3887	7781627747028358	bootloader.elf	fced577b480f0e0d
3887	4176	7781627751368455	.bin_timestamp	e514e3b0f06691b1
3887	4176	7781627751368455	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	e514e3b0f06691b1
4176	4269	7781627751408477	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
4176	4269	7781627751408477	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
12	111	7781628547734800	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
12	111	7781628547734800	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
12	120	7781629824974826	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
12	120	7781629824974826	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
